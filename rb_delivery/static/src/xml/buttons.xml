<?xml version="1.0" encoding="UTF-8"?>
  <template xml:space="preserve">

    <t t-extend="ListView.buttons">
    <t t-jquery="button" t-operation="after">
    </t>
      <t t-jquery="button.o_list_button_add" t-operation="after">
        <button  t-if="widget.modelName == 'rb_delivery.order' "  class="btn btn-secondary oe_action_button_quick_order" 
         type="button" accesskey="a" groups="rb_delivery.role_super_manager,rb_delivery.role_manager" id="custom_quick_order_button"> <i style="margin:0 5px" class="fa fa-bolt"></i> Quick Order</button>
        <button  t-if="widget.modelName == 'rb_delivery.order' "  class="btn btn-secondary oe_action_quick_access_button_1 dynamic_show_button" 
        confirm="Are you sure you want to Proceed?" type="button" accesskey="a" groups="rb_delivery.role_super_manager,rb_delivery.role_manager"> <i class="fa fa-print"></i> </button>
        <button   t-if="widget.modelName == 'rb_delivery.order'"  class="btn btn-secondary oe_action_quick_access_button_2" 
        confirm="Are you sure you want to Proceed?" type="button" accesskey="v" groups="rb_delivery.role_super_manager,rb_delivery.role_manager"> <i class="fa fa-print"></i> </button>
        <button t-if="widget.modelName == 'rb_delivery.order'"  class="btn btn-secondary oe_action_quick_access_button_3" 
         type="button" accesskey="s" groups="rb_delivery.role_super_manager,rb_delivery.role_manager"> <i class="fa fa-refresh"></i></button>
         <button t-if="widget.modelName == 'rb_delivery.order'"  class="btn btn-secondary oe_action_button_change_state" 
         type="button" accesskey="s" groups="rb_delivery.role_super_manager,rb_delivery.role_manager"> <i class="fa fa-refresh"></i> Change state</button>
        <button t-if="widget.modelName == 'rb_delivery.address_tags'"  class="btn btn-secondary oe_action_button_create_missing_tags" 
         type="button" accesskey="s" groups="rb_delivery.role_super_manager,rb_delivery.role_manager"> <i class="fa fa-refresh"></i> Create Missing Tags</button>
         <button t-if="widget.modelName == 'rb_delivery.order'"  class="btn btn-secondary oe_action_button_public_order_link" 
         type="button" accesskey="l"> <i class="fa fa-mail-forward" style="margin: 5px"></i>Public Order</button>
        </t>
        
      <t t-jquery="div.o_list_buttons" t-operation="prepend">
         <button  t-if="widget.modelName == 'rb_delivery.multi_print_orders_money_collector'||widget.modelName == 'rb_delivery.agent_money_collection'||widget.modelName == 'rb_delivery.runsheet'||widget.modelName == 'rb_delivery.returned_money_collection'||widget.modelName == 'rb_delivery.order_logs'"  class="btn btn-secondary oe_action_button_show_orders" 
         type="button" accesskey="s" groups="rb_delivery.role_super_manager,rb_delivery.role_manager">Show orders</button>
         <!-- money collection -->
         <button  t-if="widget.modelName == 'rb_delivery.multi_print_orders_money_collector'"  class="btn btn-secondary oe_action_button_change_money_collection_state" 
         type="button" accesskey="s" groups="rb_delivery.role_super_manager,rb_delivery.role_manager"><i class="fa fa-refresh"></i> Change state</button>
         <!-- returned collection -->
         <button  t-if="widget.modelName == 'rb_delivery.returned_money_collection'"  class="btn btn-secondary oe_action_button_returned_collection_state" 
         type="button" accesskey="s" groups="rb_delivery.role_super_manager,rb_delivery.role_manager"><i class="fa fa-refresh"></i>Change state</button>
         <!-- agent returned collection -->
         <button  t-if="widget.modelName == 'rb_delivery.agent_returned_collection'"  class="btn btn-secondary oe_action_button_change_agent_returned_collection_state" 
         type="button" accesskey="s" groups="rb_delivery.role_super_manager,rb_delivery.role_manager"><i class="fa fa-refresh"></i>Change state</button>
         <!-- agent collection -->
         <button  t-if="widget.modelName == 'rb_delivery.agent_money_collection'"  class="btn btn-secondary oe_action_button_change_agent_collection_state" 
         type="button" accesskey="s" groups="rb_delivery.role_super_manager,rb_delivery.role_manager"><i class="fa fa-refresh"></i> Change state</button>
         <!-- run sheet collection -->
         <button  t-if="widget.modelName == 'rb_delivery.runsheet'"  class="btn btn-secondary oe_action_button_change_runsheet_collection_state" 
         type="button" accesskey="s" groups="rb_delivery.role_super_manager,rb_delivery.role_manager" ><i class="fa fa-refresh"></i>Change state</button>
        </t>  
        
    </t>
    
  </template>
  