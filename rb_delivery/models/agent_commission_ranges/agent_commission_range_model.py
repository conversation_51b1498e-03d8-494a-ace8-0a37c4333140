from odoo import models, fields, api
from odoo.tools.translate import _

class agent_commission_range(models.Model):
    _name = 'rb_delivery.agent_commission_range'
    _description = 'Agent Commission Range'
    _order = 'from_amount'

    driver_id = fields.Many2one(
        'rb_delivery.user',
        string=_('Driver'),
        required=True,
        ondelete='cascade',
        help=_('Select the driver to whom this commission range applies.')
    )

    from_amount = fields.Float(
        _('From'),
        required=True,
        help=_('The lower bound of the order amount for which this commission applies.')
    )

    to_amount = fields.Float(
        _('To'),
        required=True,
        help=_('The upper bound of the order amount for which this commission applies.')
    )

    commission_type = fields.Selection(
        [
            ('fixed', 'Fixed Amount'),
            ('percentage', 'Percentage')
        ],
        string='Commission Type',
        required=True,
        help=_('Choose whether the commission is a fixed amount or a percentage of the order total.')
    )

    value = fields.Float(
        'Value',
        required=True,
        help=_('Enter the commission value. If percentage type is selected, enter the percentage number (e.g., 10 for 10%).')
    )


    def write(self, vals):
        res = super(agent_commission_range, s).write(vals)
        drivers = set(self.mapped('driver_id'))
        for driver in drivers:
            ranges = self.search([('driver_id', '=', driver.id)])
            sorted_ranges = sorted(ranges, key=lambda r: r.from_amount)
            for i, rec in enumerate(sorted_ranges):
                if rec.to_amount <= rec.from_amount:
                    self.env['rb_delivery.error_log'].raise_olivery_error(870, rec.id, {})
                if i > 0:
                    prev = sorted_ranges[i-1]
                    if rec.from_amount <= prev.to_amount:
                        self.env['rb_delivery.error_log'].raise_olivery_error(871, rec.id, {
                            'from': prev.from_amount,
                            'to': prev.to_amount
                        })
        return res
